﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x14000FB8C
// Function: j_??$_Fill@PEAVCMoveMapLimitRightInfo@@V1@@std@@YAXPEAVCMoveMapLimitRightInfo@@0AEBV1@@Z

/*
 * Function: j_??$_Fill@PEAVCMoveMapLimitRightInfo@@V1@@std@@YAXPEAVCMoveMapLimitRightInfo@@0AEBV1@@Z
 * Address: 0x14000FB8C
 */

void __fastcall std::_Fill<CMoveMapLimitRightInfo *,CMoveMapLimitRightInfo>(CMoveMapLimitRightInfo *_First, CMoveMapLimitRightInfo *_Last, CMoveMapLimitRightInfo *_Val)
{
  std::_Fill<CMoveMapLimitRightInfo *,CMoveMapLimitRightInfo>(_First, _Last, _Val);
}

