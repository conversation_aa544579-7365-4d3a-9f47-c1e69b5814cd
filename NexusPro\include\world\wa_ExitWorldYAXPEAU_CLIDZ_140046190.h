﻿#pragma once

// Generated from decompiled code for NexusPro
// Original address: 0x140046190
// Function: ?wa_ExitWorld@@YAXPEAU_CLID@@@Z
// Category: world

#include "../common/WindowsTypes.h"
#include "../common/Stubs.h"

// Forward declarations
class CWorld;
class CField;
class CMap;
class CZone;
class CGameObject;
class CPlayer;

// Function declarations
// TODO: Add proper function declarations based on decompiled content

