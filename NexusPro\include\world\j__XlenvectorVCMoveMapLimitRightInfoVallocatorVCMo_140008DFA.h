﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x140008DFA
// Function: j_?_<PERSON><PERSON>@?$vector@VCMoveMapLimitRightInfo@@V?$allocator@VCMoveMapLimitRightInfo@@@std@@@std@@KAXXZ

/*
 * Function: j_?_<PERSON><PERSON>@?$vector@VCMoveMapLimitRightInfo@@V?$allocator@VCMoveMapLimitRightInfo@@@std@@@std@@KAXXZ
 * Address: 0x140008DFA
 */

void __noreturn std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::_Xlen()
{
  std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::_<PERSON><PERSON>();
}

