﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x140012DF0
// Function: j_?check_dummy@AutominePersonalMgr@@QEAA_NPEAVCMapData@@EPEAM@Z

/*
 * Function: j_?check_dummy@AutominePersonalMgr@@QEAA_NPEAVCMapData@@EPEAM@Z
 * Address: 0x140012DF0
 */

bool __fastcall AutominePersonalMgr::check_dummy(AutominePersonalMgr *this, CMapData *pMap, char byCurDummyIndex, float *pfCurPos)
{
  return AutominePersonalMgr::check_dummy(this, pMap, byCurDummyIndex, pfCurPos);
}

