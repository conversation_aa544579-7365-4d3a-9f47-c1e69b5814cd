﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x14000F0FB
// Function: j_??$upvalue_@P8CMonster@@EAAPEAVCLuaSignalReActor@@XZ@lua_tinker@@YAP8CMonster@@EAAPEAVCLuaSignalReActor@@XZPEAUlua_State@@@Z

/*
 * Function: j_??$upvalue_@P8CMonster@@EAAPEAVCLuaSignalReActor@@XZ@lua_tinker@@YAP8CMonster@@EAAPEAVCLuaSignalReActor@@XZPEAUlua_State@@@Z
 * Address: 0x14000F0FB
 */

CLuaSignalReActor *(__cdecl *__fastcall lua_tinker::upvalue_<CLuaSignalReActor * (CMonster::*)(void)>(struct lua_State *L))(CMonster *this)
{
  return lua_tinker::upvalue_<CLuaSignalReActor * (CMonster::*)(void)>(L);
}

