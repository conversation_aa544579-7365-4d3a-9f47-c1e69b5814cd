﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x14000A439
// Function: j_??$upvalue_@P6APEAVCMonster@@PEAD0MMM@Z@lua_tinker@@YAP6APEAVCMonster@@PEAD0MMM@ZPEAUlua_State@@@Z

/*
 * Function: j_??$upvalue_@P6APEAVCMonster@@PEAD0MMM@Z@lua_tinker@@YAP6APEAVCMonster@@PEAD0MMM@ZPEAUlua_State@@@Z
 * Address: 0x14000A439
 */

CMonster *(__cdecl *__fastcall lua_tinker::upvalue_<CMonster * (*)(char *,char *,float,float,float)>(struct lua_State *L))(char *, char *, float, float, float)
{
  return lua_tinker::upvalue_<CMonster * (*)(char *,char *,float,float,float)>(L);
}

