﻿#pragma once

// Generated global functions for world category
// NexusPro Server

#include "../common/WindowsTypes.h"
#include "../common/Stubs.h"

// World system constructor functions
void BossSchedule_Map_Constructor(BossSchedule_Map *pThis);
void CCircleZone_Constructor(CCircleZone *pThis);
void CMapData_Constructor(CMapData *pThis);
void CMapDisplay_Constructor(CMapDisplay *pThis);
void CMapExtend_Constructor(CMapExtend *pThis);
void CMapOperation_Constructor(CMapOperation *pThis);
void CMapTab_Constructor(CMapTab *pThis);
void CMonsterAggroMgr_Constructor(CMonsterAggroMgr *pThis);
void CMonsterAI_Constructor(CMonsterAI *pThis);
void CMonsterEventRespawn_Constructor(CMonsterEventRespawn *pThis);
void CMonsterEventSet_Constructor(CMonsterEventSet *pThis);
void CMonsterHierarchy_Constructor(CMonsterHierarchy *pThis);
void CMonster_Constructor(CMonster *pThis);
void CMoveMapLimitInfoList_Constructor(CMoveMapLimitInfoList *pThis);
void CMoveMapLimitInfoPortal_Constructor(CMoveMapLimitInfoPortal *pThis, unsigned int uiInx, int iType);
void CMoveMapLimitInfo_Constructor(CMoveMapLimitInfo *pThis, unsigned int uiInx, int iType);
void CMoveMapLimitManager_Constructor(CMoveMapLimitManager *pThis);
void CMoveMapLimitRightInfoList_Constructor(CMoveMapLimitRightInfoList *pThis);
void CMoveMapLimitRightInfo_Constructor(CMoveMapLimitRightInfo *pThis);
void CMoveMapLimitRightPortal_Constructor(CMoveMapLimitRightPortal *pThis, int iType);
void CMoveMapLimitRight_Constructor(CMoveMapLimitRight *pThis, int iType);
void CRFMonsterAIMgr_Constructor(CRFMonsterAIMgr *pThis);
void CWorldSchedule_Constructor(CWorldSchedule *pThis);
void MonsterSetInfoData_Constructor(MonsterSetInfoData *pThis);
void MonsterStateData_Constructor(MonsterStateData *pThis);
void event_respawn_Constructor(_event_respawn *pThis);
void map_fld_Constructor(_map_fld *pThis);
void monster_create_setdata_Constructor(_monster_create_setdata *pThis);
void monster_sp_group_Constructor(_monster_sp_group *pThis);
void notice_move_limit_map_msg_zocl_Constructor(_notice_move_limit_map_msg_zocl *pThis);
void NPCQuestIndexTempData_Constructor(_NPCQuestIndexTempData *pThis);
void npc_create_setdata_Constructor(_npc_create_setdata *pThis);
void npc_quest_list_result_zocl_Constructor(_npc_quest_list_result_zocl *pThis);
void reged_char_result_zone_Constructor(_reged_char_result_zone *pThis);
void target_monster_aggro_inform_zocl_Constructor(_target_monster_aggro_inform_zocl *pThis);
void target_monster_contsf_allinform_zocl_Constructor(_target_monster_contsf_allinform_zocl *pThis);
void add_monster_Constructor(__add_monster *pThis);
void change_monster_Constructor(__change_monster *pThis);
void monster_group_Constructor(__monster_group *pThis);
void respawn_monster_Constructor(__respawn_monster *pThis);
// World system destructor functions
void BossSchedule_Map_Destructor(BossSchedule_Map *pThis);
void CCircleZone_Destructor(CCircleZone *pThis);
void CMapData_Destructor(CMapData *pThis);
void CMapDisplay_Destructor(CMapDisplay *pThis);
void CMapExtend_Destructor(CMapExtend *pThis);
void CMapOperation_Destructor(CMapOperation *pThis);
void CMapTab_Destructor(CMapTab *pThis);
void CMonsterAggroMgr_Destructor(CMonsterAggroMgr *pThis);
void CMonsterAI_Destructor(CMonsterAI *pThis);
void CMonsterEventRespawn_Destructor(CMonsterEventRespawn *pThis);
void CMonsterEventSet_Destructor(CMonsterEventSet *pThis);
void CMonsterHierarchy_Destructor(CMonsterHierarchy *pThis);
void CMonster_Destructor(CMonster *pThis);
void CMoveMapLimitInfoList_Destructor(CMoveMapLimitInfoList *pThis);
void CMoveMapLimitInfoPortal_Destructor(CMoveMapLimitInfoPortal *pThis);
void CMoveMapLimitInfo_Destructor(CMoveMapLimitInfo *pThis);
void CMoveMapLimitManager_Destructor(CMoveMapLimitManager *pThis);
void CMoveMapLimitRightInfoList_Destructor(CMoveMapLimitRightInfoList *pThis);
void CMoveMapLimitRightInfo_Destructor(CMoveMapLimitRightInfo *pThis);
void CMoveMapLimitRightPortal_Destructor(CMoveMapLimitRightPortal *pThis);
void CMoveMapLimitRight_Destructor(CMoveMapLimitRight *pThis);
void CRFMonsterAIMgr_Destructor(CRFMonsterAIMgr *pThis);
void CWorldSchedule_Destructor(CWorldSchedule *pThis);
void MonsterSetInfoData_Destructor(MonsterSetInfoData *pThis);
void MonsterStateData_Destructor(MonsterStateData *pThis);

// World system utility functions
void LoadMapData(const char* mapFile);
void InitializeWorldSystem();
void CleanupWorldSystem();
void ProcessMonsterAI();
void UpdateWorldSchedule();
void HandleMapOperations();
void ProcessMoveMapLimits();
void UpdateMonsterAggro();
void HandleEventRespawn();
void ProcessNPCQuests();
void UpdateMonsterHierarchy();
void HandleCircleZones();
void ProcessMapDisplay();
void UpdateMapExtensions();
void HandleMapTabs();
void ProcessMonsterEvents();
void UpdateWorldState();
