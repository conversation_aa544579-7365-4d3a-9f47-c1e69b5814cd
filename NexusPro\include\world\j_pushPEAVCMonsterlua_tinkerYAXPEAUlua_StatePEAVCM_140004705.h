﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x140004705
// Function: j_??$push@PEAVCMonster@@@lua_tinker@@YAXPEAUlua_State@@PEAVCMonster@@@Z

/*
 * Function: j_??$push@PEAVCMonster@@@lua_tinker@@YAXPEAUlua_State@@PEAVCMonster@@@Z
 * Address: 0x140004705
 */

void __fastcall lua_tinker::push<CMonster *>(struct lua_State *L, CMonster *ret)
{
  lua_tinker::push<CMonster *>(L, ret);
}

