﻿#pragma once

// Generated from decompiled code for NexusPro
// Original address: 0x140013C91
// Function: j_??$_Fill_n@PEAPEAVCMoveMapLimitRight@@_KPEAV1@@std@@YAXPEAPEAVCMoveMapLimitRight@@_KAEBQEAV1@U_Range_checked_iterator_tag@0@@Z
// Category: world

#include "../common/WindowsTypes.h"
#include "../common/Stubs.h"

// Forward declarations
class CWorld;
class CField;
class CMap;
class CZone;
class CGameObject;
class CPlayer;

// Function declarations
// TODO: Add proper function declarations based on decompiled content

