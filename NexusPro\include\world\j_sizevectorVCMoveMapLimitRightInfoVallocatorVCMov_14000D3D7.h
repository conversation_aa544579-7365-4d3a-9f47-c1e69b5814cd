﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x14000D3D7
// Function: j_?size@?$vector@VCMoveMapLimitRightInfo@@V?$allocator@VCMoveMapLimitRightInfo@@@std@@@std@@QEBA_KXZ

/*
 * Function: j_?size@?$vector@VCMoveMapLimitRightInfo@@V?$allocator@VCMoveMapLimitRightInfo@@@std@@@std@@QEBA_KXZ
 * Address: 0x14000D3D7
 */

unsigned __int64 __fastcall std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::size(std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *this)
{
  return std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::size(this);
}

