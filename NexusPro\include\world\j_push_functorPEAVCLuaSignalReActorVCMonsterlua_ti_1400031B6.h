﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x1400031B6
// Function: j_??$push_functor@PEAVCLuaSignalReActor@@VCMonster@@@lua_tinker@@YAXPEAUlua_State@@P8CMonster@@EAAPEAVCLuaSignalReActor@@XZ@Z

/*
 * Function: j_??$push_functor@PEAVCLuaSignalReActor@@VCMonster@@@lua_tinker@@YAXPEAUlua_State@@P8CMonster@@EAAPEAVCLuaSignalReActor@@XZ@Z
 * Address: 0x1400031B6
 */

void __fastcall lua_tinker::push_functor<CLuaSignalReActor *,CMonster>(struct lua_State *L, CLuaSignalReActor *(__cdecl *func)(CMonster *this))
{
  lua_tinker::push_functor<CLuaSignalReActor *,CMonster>(L, func);
}

