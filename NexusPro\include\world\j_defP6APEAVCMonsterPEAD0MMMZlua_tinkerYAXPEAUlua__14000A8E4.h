﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x14000A8E4
// Function: j_??$def@P6APEAVCMonster@@PEAD0MMM@Z@lua_tinker@@YAXPEAUlua_State@@PEBDP6APEAVCMonster@@PEAD2MMM@Z@Z

/*
 * Function: j_??$def@P6APEAVCMonster@@PEAD0MMM@Z@lua_tinker@@YAXPEAUlua_State@@PEBDP6APEAVCMonster@@PEAD2MMM@Z@Z
 * Address: 0x14000A8E4
 */

void __fastcall lua_tinker::def<CMonster * (*)(char *,char *,float,float,float)>(struct lua_State *L, const char *name, CMonster *(__cdecl *func)(char *, char *, float, float, float))
{
  lua_tinker::def<CMonster * (*)(char *,char *,float,float,float)>(L, name, func);
}

