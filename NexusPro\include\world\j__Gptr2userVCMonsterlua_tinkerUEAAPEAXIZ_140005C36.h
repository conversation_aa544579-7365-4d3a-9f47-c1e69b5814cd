﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x140005C36
// Function: j_??_G?$ptr2user@VCMonster@@@lua_tinker@@UEAAPEAXI@Z

/*
 * Function: j_??_G?$ptr2user@VCMonster@@@lua_tinker@@UEAAPEAXI@Z
 * Address: 0x140005C36
 */

void *__fastcall lua_tinker::ptr2user<CMonster>::`scalar deleting destructor'(lua_tinker::ptr2user<CMonster> *this, unsigned int a2)
{
  return lua_tinker::ptr2user<CMonster>::`scalar deleting destructor'(this, a2);
}

