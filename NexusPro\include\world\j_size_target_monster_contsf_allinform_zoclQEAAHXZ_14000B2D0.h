﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x14000B2D0
// Function: j_?size@_target_monster_contsf_allinform_zocl@@QEAAHXZ

/*
 * Function: j_?size@_target_monster_contsf_allinform_zocl@@QEAAHXZ
 * Address: 0x14000B2D0
 */

int __fastcall _target_monster_contsf_allinform_zocl::size(_target_monster_contsf_allinform_zocl *this)
{
  return _target_monster_contsf_allinform_zocl::size(this);
}

