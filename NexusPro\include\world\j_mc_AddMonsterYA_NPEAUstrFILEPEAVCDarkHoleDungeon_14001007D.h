﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x14001007D
// Function: j_?mc_Add<PERSON>onster@@YA_NPEAUstrFILE@@PEAVCDarkHoleDungeonQuestSetup@@PEAD@Z

/*
 * Function: j_?mc_AddMonster@@YA_NPEAUstrFILE@@PEAVCDarkHoleDungeonQuestSetup@@PEAD@Z
 * Address: 0x14001007D
 */

bool __fastcall mc_AddMonster(strFILE *fstr, CDarkHoleDungeonQuestSetup *pSetup, char *pszoutErrMsg)
{
  return mc_AddMonster(fstr, pSetup, pszoutErrMsg);
}

