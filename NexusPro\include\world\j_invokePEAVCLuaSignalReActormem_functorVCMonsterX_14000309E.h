﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x14000309E
// Function: j_??$invoke@PEAVCLuaSignalReActor@@@?$mem_functor@VCMonster@@XXXXX@lua_tinker@@SAHPEAUlua_State@@@Z

/*
 * Function: j_??$invoke@PEAVCLuaSignalReActor@@@?$mem_functor@VCMonster@@XXXXX@lua_tinker@@SAHPEAUlua_State@@@Z
 * Address: 0x14000309E
 */

int lua_tinker::mem_functor<CMonster,void,void,void,void,void>::invoke<CLuaSignalReActor *>()
{
  return lua_tinker::mem_functor<CMonster,void,void,void,void,void>::invoke<CLuaSignalReActor *>();
}

