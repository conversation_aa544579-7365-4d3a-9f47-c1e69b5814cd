﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x140003A3F
// Function: j_?invoke@?$object2lua@PEAVCMonster@@@lua_tinker@@SAXPEAUlua_State@@PEAVCMonster@@@Z

/*
 * Function: j_?invoke@?$object2lua@PEAVCMonster@@@lua_tinker@@SAXPEAUlua_State@@PEAVCMonster@@@Z
 * Address: 0x140003A3F
 */

void __fastcall lua_tinker::object2lua<CMonster *>::invoke(lua_tinker::object2lua<CMonster *> *this, struct lua_State *L, CMonster *val)
{
  lua_tinker::object2lua<CMonster *>::invoke(this, L, val);
}

