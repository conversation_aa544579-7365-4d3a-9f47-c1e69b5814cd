﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x1400086DE
// Function: j_?allocate@?$allocator@PEAVCMoveMapLimitRight@@@std@@QEAAPEAPEAVCMoveMapLimitRight@@_K@Z

/*
 * Function: j_?allocate@?$allocator@PEAVCMoveMapLimitRight@@@std@@QEAAPEAPEAVCMoveMapLimitRight@@_K@Z
 * Address: 0x1400086DE
 */

CMoveMapLimitRight **__fastcall std::allocator<CMoveMapLimitRight *>::allocate(std::allocator<CMoveMapLimitRight *> *this, unsigned __int64 _Count)
{
  return std::allocator<CMoveMapLimitRight *>::allocate(this, _Count);
}

