﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x140001451
// Function: j_??$_Destroy@VCMoveMapLimitRightInfo@@@std@@YAXPEAVCMoveMapLimitRightInfo@@@Z

/*
 * Function: j_??$_Destroy@VCMoveMapLimitRightInfo@@@std@@YAXPEAVCMoveMapLimitRightInfo@@@Z
 * Address: 0x140001451
 */

void __fastcall std::_Destroy<CMoveMapLimitRightInfo>(CMoveMapLimitRightInfo *_Ptr)
{
  std::_Destroy<CMoveMapLimitRightInfo>(_Ptr);
}

