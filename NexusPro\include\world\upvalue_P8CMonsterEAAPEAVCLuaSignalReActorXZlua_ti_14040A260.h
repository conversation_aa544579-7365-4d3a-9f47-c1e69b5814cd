﻿#pragma once

// Generated from decompiled code for NexusPro
// Original address: 0x14040A260
// Function: ??$upvalue_@P8CMonster@@EAAPEAVCLuaSignalReActor@@XZ@lua_tinker@@YAP8CMonster@@EAAPEAVCLuaSignalReActor@@XZPEAUlua_State@@@Z
// Category: world

#include "../common/WindowsTypes.h"
#include "../common/Stubs.h"

// Forward declarations
class CWorld;
class CField;
class CMap;
class CZone;
class CGameObject;
class CPlayer;

// Function declarations
// TODO: Add proper function declarations based on decompiled content

