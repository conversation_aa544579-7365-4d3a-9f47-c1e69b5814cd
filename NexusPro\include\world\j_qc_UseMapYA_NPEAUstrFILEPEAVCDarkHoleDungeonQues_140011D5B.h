﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x140011D5B
// Function: j_?qc_UseMap@@YA_NPEAUstrFILE@@PEAVCDarkHoleDungeonQuestSetup@@PEAD@Z

/*
 * Function: j_?qc_UseMap@@YA_NPEAUstrFILE@@PEAVCDarkHoleDungeonQuestSetup@@PEAD@Z
 * Address: 0x140011D5B
 */

bool __fastcall qc_UseMap(strFILE *fstr, CDarkHoleDungeonQuestSetup *pSetup, char *pszoutErrMsg)
{
  return qc_UseMap(fstr, pSetup, pszoutErrMsg);
}

