﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x14000C635
// Function: j_??_GCMonsterEventRespawn@@UEAAPEAXI@Z_0

/*
 * Function: j_??_GCMonsterEventRespawn@@UEAAPEAXI@Z_0
 * Address: 0x14000C635
 */

void *__fastcall CMonsterEventRespawn::`scalar deleting destructor'(CMonsterEventRespawn *this, unsigned int a2)
{
  return CMonsterEventRespawn::`scalar deleting destructor'(this, a2);
}

