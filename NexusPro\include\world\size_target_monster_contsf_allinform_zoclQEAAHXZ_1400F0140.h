﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x1400F0140
// Function: ?size@_target_monster_contsf_allinform_zocl@@QEAAHXZ

/*
 * Function: ?size@_target_monster_contsf_allinform_zocl@@QEAAHXZ
 * Address: 0x1400F0140
 */

signed __int64 __fastcall _target_monster_contsf_allinform_zocl::size(_target_monster_contsf_allinform_zocl *this)
{
  if ( this->byContCount > 8 )
    this->byContCount = 0;
  return 21 - 2i64 * (8 - this->byContCount);
}

