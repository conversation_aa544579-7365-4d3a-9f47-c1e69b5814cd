﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x1400129C2
// Function: j_?max_size@?$allocator@PEAVCMoveMapLimitInfo@@@std@@QEBA_KXZ

/*
 * Function: j_?max_size@?$allocator@PEAVCMoveMapLimitInfo@@@std@@QEBA_KXZ
 * Address: 0x1400129C2
 */

unsigned __int64 __fastcall std::allocator<CMoveMapLimitInfo *>::max_size(std::allocator<CMoveMapLimitInfo *> *this)
{
  return std::allocator<CMoveMapLimitInfo *>::max_size(this);
}

