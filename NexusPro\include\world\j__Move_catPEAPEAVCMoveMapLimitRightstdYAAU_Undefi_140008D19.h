﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x140008D19
// Function: j_??$_Move_cat@PEAPEAVCMoveMapLimitRight@@@std@@YA?AU_Undefined_move_tag@0@AEBQEAPEAVCMoveMapLimitRight@@@Z

/*
 * Function: j_??$_Move_cat@PEAPEAVCMoveMapLimitRight@@@std@@YA?AU_Undefined_move_tag@0@AEBQEAPEAVCMoveMapLimitRight@@@Z
 * Address: 0x140008D19
 */

std::_Undefined_move_tag __fastcall std::_Move_cat<CMoveMapLimitRight * *>(CMoveMapLimitRight **const *__formal)
{
  return std::_Move_cat<CMoveMapLimitRight * *>(__formal);
}

