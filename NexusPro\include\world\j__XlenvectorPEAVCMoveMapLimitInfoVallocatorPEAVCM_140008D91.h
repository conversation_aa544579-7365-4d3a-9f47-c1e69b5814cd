﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x140008D91
// Function: j_?_<PERSON><PERSON>@?$vector@PEAVCMoveMapLimitInfo@@V?$allocator@PEAVCMoveMapLimitInfo@@@std@@@std@@KAXXZ

/*
 * Function: j_?_<PERSON>len@?$vector@PEAVCMoveMapLimitInfo@@V?$allocator@PEAVCMoveMapLimitInfo@@@std@@@std@@KAXXZ
 * Address: 0x140008D91
 */

void __fastcall __noreturn std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::_Xlen(std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *this)
{
  std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::_<PERSON><PERSON>(this);
}

