﻿#pragma once

// Generated from decompiled code for NexusPro
// Original address: 0x14040A3A0
// Function: ??$upvalue_@P6APEAVCMonster@@PEAD0MMM@Z@lua_tinker@@YAP6APEAVCMonster@@PEAD0MMM@ZPEAUlua_State@@@Z
// Category: world

#include "../common/WindowsTypes.h"
#include "../common/Stubs.h"

// Forward declarations
class CWorld;
class CField;
class CMap;
class CZone;
class CGameObject;
class CPlayer;

// Function declarations
// TODO: Add proper function declarations based on decompiled content

