﻿#pragma once

// Generated from decompiled code for NexusPro
// Original address: 0x14000C856
// Function: j_??$_Unchecked_move_backward@PEAVCMoveMapLimitRightInfo@@PEAV1@@stdext@@YAPEAVCMoveMapLimitRightInfo@@PEAV1@00@Z
// Category: world

#include "../common/WindowsTypes.h"
#include "../common/Stubs.h"

// Forward declarations
class CWorld;
class CField;
class CMap;
class CZone;
class CGameObject;
class CPlayer;

// Function declarations
// TODO: Add proper function declarations based on decompiled content

