﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x140004ABB
// Function: j_?mc_RespawnMonsterOption@@YA_NPEAUstrFILE@@PEAVCDarkHoleDungeonQuestSetup@@PEAD@Z

/*
 * Function: j_?mc_RespawnMonsterOption@@YA_NPEAUstrFILE@@PEAVCDarkHoleDungeonQuestSetup@@PEAD@Z
 * Address: 0x140004ABB
 */

bool __fastcall mc_RespawnMonsterOption(strFILE *fstr, CDarkHoleDungeonQuestSetup *pSetup, char *pszoutErrMsg)
{
  return mc_RespawnMonsterOption(fstr, pSetup, pszoutErrMsg);
}

