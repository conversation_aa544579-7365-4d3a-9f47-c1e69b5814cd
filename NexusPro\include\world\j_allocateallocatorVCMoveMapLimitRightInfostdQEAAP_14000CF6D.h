﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x14000CF6D
// Function: j_?allocate@?$allocator@VCMoveMapLimitRightInfo@@@std@@QEAAPEAVCMoveMapLimitRightInfo@@_K@Z

/*
 * Function: j_?allocate@?$allocator@VCMoveMapLimitRightInfo@@@std@@QEAAPEAVCMoveMapLimitRightInfo@@_K@Z
 * Address: 0x14000CF6D
 */

CMoveMapLimitRightInfo *__fastcall std::allocator<CMoveMapLimitRightInfo>::allocate(std::allocator<CMoveMapLimitRightInfo> *this, unsigned __int64 _Count)
{
  return std::allocator<CMoveMapLimitRightInfo>::allocate(this, _Count);
}

