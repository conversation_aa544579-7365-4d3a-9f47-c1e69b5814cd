﻿#pragma once

// Generated from decompiled code for NexusPro
// Original address: 0x1403B21A0
// Function: ??$unchecked_copy@PEAVCMoveMapLimitRightInfo@@PEAV1@@stdext@@YAPEAVCMoveMapLimitRightInfo@@PEAV1@00@Z
// Category: world

#include "../common/WindowsTypes.h"
#include "../common/Stubs.h"

// Forward declarations
class CWorld;
class CField;
class CMap;
class CZone;
class CGameObject;
class CPlayer;

// Function declarations
// TODO: Add proper function declarations based on decompiled content

