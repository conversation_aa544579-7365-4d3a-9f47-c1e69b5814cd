﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x14000A894
// Function: j_?max_size@?$allocator@PEAVCMoveMapLimitRight@@@std@@QEBA_KXZ

/*
 * Function: j_?max_size@?$allocator@PEAVCMoveMapLimitRight@@@std@@QEBA_KXZ
 * Address: 0x14000A894
 */

unsigned __int64 __fastcall std::allocator<CMoveMapLimitRight *>::max_size(std::allocator<CMoveMapLimitRight *> *this)
{
  return std::allocator<CMoveMapLimitRight *>::max_size(this);
}

