﻿#pragma once

// Generated from decompiled code for NexusPro
// Original address: 0x14001023F
// Function: j_?invoke@?$user2type@P6APEAVCMonster@@PEAD0MMM@Z@lua_tinker@@SAP6APEAVCMonster@@PEAD0MMM@ZPEAUlua_State@@H@Z
// Category: world

#include "../common/WindowsTypes.h"
#include "../common/Stubs.h"

// Forward declarations
class CWorld;
class CField;
class CMap;
class CZone;
class CGameObject;
class CPlayer;

// Function declarations
// TODO: Add proper function declarations based on decompiled content

