﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x14000C162
// Function: j_?_LoadMonBlk@CMapData@@AEAA_NPEADPEAU_map_fld@@@Z

/*
 * Function: j_?_Load<PERSON>onBlk@CMapData@@AEAA_NPEADPEAU_map_fld@@@Z
 * Address: 0x14000C162
 */

bool __fastcall CMapData::_LoadMonBlk(CMapData *this, char *pszMapCode, _map_fld *pMapFld)
{
  return CMapData::_LoadMonBlk(this, pszMapCode, pMapFld);
}

