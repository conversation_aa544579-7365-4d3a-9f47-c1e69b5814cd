﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x14000267B
// Function: j_?unregist_from_map@AutominePersonal@@QEAA_NE@Z

/*
 * Function: j_?unregist_from_map@AutominePersonal@@QEAA_NE@Z
 * Address: 0x14000267B
 */

bool __fastcall AutominePersonal::unregist_from_map(AutominePersonal *this, char byDestroyType)
{
  return AutominePersonal::unregist_from_map(this, byDestroyType);
}

