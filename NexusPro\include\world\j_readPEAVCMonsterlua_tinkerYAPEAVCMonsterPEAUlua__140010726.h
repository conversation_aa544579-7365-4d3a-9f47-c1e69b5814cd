﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x140010726
// Function: j_??$read@PEAVCMonster@@@lua_tinker@@YAPEAVCMonster@@PEAUlua_State@@H@Z

/*
 * Function: j_??$read@PEAVCMonster@@@lua_tinker@@YAPEAVCMonster@@PEAUlua_State@@H@Z
 * Address: 0x140010726
 */

CMonster *__fastcall lua_tinker::read<CMonster *>(struct lua_State *L, int index)
{
  return lua_tinker::read<CMonster *>(L, index);
}

