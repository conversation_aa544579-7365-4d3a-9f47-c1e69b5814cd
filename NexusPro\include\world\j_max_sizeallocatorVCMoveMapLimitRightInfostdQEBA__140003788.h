﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x140003788
// Function: j_?max_size@?$allocator@VCMoveMapLimitRightInfo@@@std@@QEBA_KXZ

/*
 * Function: j_?max_size@?$allocator@VCMoveMapLimitRightInfo@@@std@@QEBA_KXZ
 * Address: 0x140003788
 */

unsigned __int64 __fastcall std::allocator<CMoveMapLimitRightInfo>::max_size(std::allocator<CMoveMapLimitRightInfo> *this)
{
  return std::allocator<CMoveMapLimitRightInfo>::max_size(this);
}

