﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x140002BC1
// Function: j_?mc_<PERSON>spawnMons<PERSON>@@YA_NPEAUstrFILE@@PEAVCDarkHoleDungeonQuestSetup@@PEAD@Z

/*
 * Function: j_?mc_RespawnMonster@@YA_NPEAUstrFILE@@PEAVCDarkHoleDungeonQuestSetup@@PEAD@Z
 * Address: 0x140002BC1
 */

bool __fastcall mc_RespawnMonster(strFILE *fstr, CDarkHoleDungeonQuestSetup *pSetup, char *pszoutErrMsg)
{
  return mc_RespawnMonster(fstr, pSetup, pszoutErrMsg);
}

