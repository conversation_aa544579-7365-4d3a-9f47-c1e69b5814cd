﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x14000FA6F
// Function: j_??$_Fill@PEAPEAVCMoveMapLimitInfo@@PEAV1@@std@@YAXPEAPEAVCMoveMapLimitInfo@@0AEBQEAV1@@Z

/*
 * Function: j_??$_Fill@PEAPEAVCMoveMapLimitInfo@@PEAV1@@std@@YAXPEAPEAVCMoveMapLimitInfo@@0AEBQEAV1@@Z
 * Address: 0x14000FA6F
 */

void __fastcall std::_Fill<CMoveMapLimitInfo * *,CMoveMapLimitInfo *>(CMoveMapLimitInfo **_First, CMoveMapLimitInfo **_Last, CMoveMapLimitInfo *const *_Val)
{
  std::_Fill<CMoveMapLimitInfo * *,CMoveMapLimitInfo *>(_First, _Last, _Val);
}

