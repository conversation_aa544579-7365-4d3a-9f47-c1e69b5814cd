﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x1400066DB
// Function: j_?qc_monsterGroup@@YA_NPEAUstrFILE@@PEAVCDarkHoleDungeonQuestSetup@@PEAD@Z

/*
 * Function: j_?qc_monsterGroup@@YA_NPEAUstrFILE@@PEAVCDarkHoleDungeonQuestSetup@@PEAD@Z
 * Address: 0x1400066DB
 */

bool __fastcall qc_monsterGroup(strFILE *fstr, CDarkHoleDungeonQuestSetup *pSetup, char *pszoutErrMsg)
{
  return qc_monsterGroup(fstr, pSetup, pszoutErrMsg);
}

