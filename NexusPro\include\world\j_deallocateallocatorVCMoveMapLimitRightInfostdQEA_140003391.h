﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x140003391
// Function: j_?deallocate@?$allocator@VCMoveMapLimitRightInfo@@@std@@QEAAXPEAVCMoveMapLimitRightInfo@@_K@Z

/*
 * Function: j_?deallocate@?$allocator@VCMoveMapLimitRightInfo@@@std@@QEAAXPEAVCMoveMapLimitRightInfo@@_K@Z
 * Address: 0x140003391
 */

void __fastcall std::allocator<CMoveMapLimitRightInfo>::deallocate(std::allocator<CMoveMapLimitRightInfo> *this, CMoveMapLimitRightInfo *_Ptr, unsigned __int64 __formal)
{
  std::allocator<CMoveMapLimitRightInfo>::deallocate(this, _Ptr, __formal);
}

