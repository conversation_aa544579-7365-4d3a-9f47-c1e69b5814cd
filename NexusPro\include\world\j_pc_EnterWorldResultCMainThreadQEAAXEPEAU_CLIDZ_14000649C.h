﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x14000649C
// Function: j_?pc_EnterWorldResult@CMainThread@@QEAAXEPEAU_CLID@@@Z

/*
 * Function: j_?pc_EnterWorldResult@CMainThread@@QEAAXEPEAU_CLID@@@Z
 * Address: 0x14000649C
 */

void __fastcall CMainThread::pc_EnterWorldResult(CMainThread *this, char byRetCode, _CLID *pidWorld)
{
  CMainThread::pc_EnterWorldResult(this, byRetCode, pidWorld);
}

