﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x140006AAF
// Function: j_??$_Destroy@PEAVCMoveMapLimitRight@@@std@@YAXPEAPEAVCMoveMapLimitRight@@@Z

/*
 * Function: j_??$_Destroy@PEAVCMoveMapLimitRight@@@std@@YAXPEAPEAVCMoveMapLimitRight@@@Z
 * Address: 0x140006AAF
 */

void __fastcall std::_Destroy<CMoveMapLimitRight *>(CMoveMapLimitRight **_Ptr)
{
  std::_Destroy<CMoveMapLimitRight *>(_Ptr);
}

