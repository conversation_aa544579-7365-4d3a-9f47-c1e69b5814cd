﻿#pragma once

// Generated from decompiled code for NexusPro
// Class: ptr2lua_CMonster_
// Category: world

#include "../common/WindowsTypes.h"
#include "../common/Stubs.h"

// Forward declarations

class ptr2lua_CMonster_ {
public:
    // Constructor/Destructor
    ptr2lua_CMonster_();
    virtual ~ptr2lua_CMonster_();

    // Methods
    void _lua_tinker(__int64 a1, __int64 a2);

    // Member variables (add as discovered from decompiled code)
    // TODO: Add member variables based on usage analysis

private:
    // Private members as needed
};
