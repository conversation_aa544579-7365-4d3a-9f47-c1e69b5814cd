﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x14001348A
// Function: j_??$_Iter_cat@PEAPEAVCMoveMapLimitInfo@@@std@@YA?AUrandom_access_iterator_tag@0@AEBQEAPEAVCMoveMapLimitInfo@@@Z

/*
 * Function: j_??$_Iter_cat@PEAPEAVCMoveMapLimitInfo@@@std@@YA?AUrandom_access_iterator_tag@0@AEBQEAPEAVCMoveMapLimitInfo@@@Z
 * Address: 0x14001348A
 */

std::random_access_iterator_tag __fastcall std::_Iter_cat<CMoveMapLimitInfo * *>(CMoveMapLimitInfo **const *__formal)
{
  return std::_Iter_cat<CMoveMapLimitInfo * *>(__formal);
}

