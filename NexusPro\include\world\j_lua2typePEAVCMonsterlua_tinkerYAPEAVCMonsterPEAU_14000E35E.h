﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x14000E35E
// Function: j_??$lua2type@PEAVCMonster@@@lua_tinker@@YAPEAVCMonster@@PEAUlua_State@@H@Z

/*
 * Function: j_??$lua2type@PEAVCMonster@@@lua_tinker@@YAPEAVCMonster@@PEAUlua_State@@H@Z
 * Address: 0x14000E35E
 */

CMonster *__fastcall lua_tinker::lua2type<CMonster *>(struct lua_State *L, int index)
{
  return lua_tinker::lua2type<CMonster *>(L, index);
}

