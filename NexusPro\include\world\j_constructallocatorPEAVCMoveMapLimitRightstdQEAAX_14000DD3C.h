﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x14000DD3C
// Function: j_?construct@?$allocator@PEAVCMoveMapLimitRight@@@std@@QEAAXPEAPEAVCMoveMapLimitRight@@AEBQEAV3@@Z

/*
 * Function: j_?construct@?$allocator@PEAVCMoveMapLimitRight@@@std@@QEAAXPEAPEAVCMoveMapLimitRight@@AEBQEAV3@@Z
 * Address: 0x14000DD3C
 */

void __fastcall std::allocator<CMoveMapLimitRight *>::construct(std::allocator<CMoveMapLimitRight *> *this, CMoveMapLimitRight **_Ptr, CMoveMapLimitRight *const *_Val)
{
  std::allocator<CMoveMapLimitRight *>::construct(this, _Ptr, _Val);
}

