﻿#pragma once

// Generated from decompiled code for NexusPro
// Original address: 0x140002347
// Function: j_??$_Move_backward_opt@PEAPEAVCMoveMapLimitInfo@@PEAPEAV1@Urandom_access_iterator_tag@std@@U_Undefined_move_tag@3@@std@@YAPEAPEAVCMoveMapLimitInfo@@PEAPEAV1@00Urandom_access_iterator_tag@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
// Category: world

#include "../common/WindowsTypes.h"
#include "../common/Stubs.h"

// Forward declarations
class CWorld;
class CField;
class CMap;
class CZone;
class CGameObject;
class CPlayer;

// Function declarations
// TODO: Add proper function declarations based on decompiled content

