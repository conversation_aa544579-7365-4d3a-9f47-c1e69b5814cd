﻿#pragma once

// Generated from decompiled code for NexusPro
// Original address: 0x14000DDFF
// Function: j_?insert@?$vector@VCMoveMapLimitRightInfo@@V?$allocator@VCMoveMapLimitRightInfo@@@std@@@std@@QEAAXV?$_Vector_iterator@VCMoveMapLimitRightInfo@@V?$allocator@VCMoveMapLimitRightInfo@@@std@@@2@_KAEBVCMoveMapLimitRightInfo@@@Z
// Category: world

#include "../common/WindowsTypes.h"
#include "../common/Stubs.h"

// Forward declarations
class CWorld;
class CField;
class CMap;
class CZone;
class CGameObject;
class CPlayer;

// Function declarations
// TODO: Add proper function declarations based on decompiled content

