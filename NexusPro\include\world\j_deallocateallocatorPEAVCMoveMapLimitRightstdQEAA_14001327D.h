﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x14001327D
// Function: j_?deallocate@?$allocator@PEAVCMoveMapLimitRight@@@std@@QEAAXPEAPEAVCMoveMapLimitRight@@_K@Z

/*
 * Function: j_?deallocate@?$allocator@PEAVCMoveMapLimitRight@@@std@@QEAAXPEAPEAVCMoveMapLimitRight@@_K@Z
 * Address: 0x14001327D
 */

void __fastcall std::allocator<CMoveMapLimitRight *>::deallocate(std::allocator<CMoveMapLimitRight *> *this, CMoveMapLimitRight **_Ptr, unsigned __int64 __formal)
{
  std::allocator<CMoveMapLimitRight *>::deallocate(this, _Ptr, __formal);
}

