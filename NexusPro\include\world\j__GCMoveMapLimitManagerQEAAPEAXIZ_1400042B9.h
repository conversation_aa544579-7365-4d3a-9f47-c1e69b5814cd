﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x1400042B9
// Function: j_??_GCMoveMapLimitManager@@QEAAPEAXI@Z

/*
 * Function: j_??_GCMoveMapLimitManager@@QEAAPEAXI@Z
 * Address: 0x1400042B9
 */

void *__fastcall CMoveMapLimitManager::`scalar deleting destructor'(CMoveMapLimitManager *this, unsigned int a2)
{
  return CMoveMapLimitManager::`scalar deleting destructor'(this, a2);
}

