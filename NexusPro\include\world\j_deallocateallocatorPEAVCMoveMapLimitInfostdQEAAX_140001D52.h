﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x140001D52
// Function: j_?deallocate@?$allocator@PEAVCMoveMapLimitInfo@@@std@@QEAAXPEAPEAVCMoveMapLimitInfo@@_K@Z

/*
 * Function: j_?deallocate@?$allocator@PEAVCMoveMapLimitInfo@@@std@@QEAAXPEAPEAVCMoveMapLimitInfo@@_K@Z
 * Address: 0x140001D52
 */

void __fastcall std::allocator<CMoveMapLimitInfo *>::deallocate(std::allocator<CMoveMapLimitInfo *> *this, CMoveMapLimitInfo **_Ptr, unsigned __int64 __formal)
{
  std::allocator<CMoveMapLimitInfo *>::deallocate(this, _Ptr, __formal);
}

