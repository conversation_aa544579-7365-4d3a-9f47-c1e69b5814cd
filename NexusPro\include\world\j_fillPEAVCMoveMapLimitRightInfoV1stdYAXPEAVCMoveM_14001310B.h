﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x14001310B
// Function: j_??$fill@PEAVCMoveMapLimitRightInfo@@V1@@std@@YAXPEAVCMoveMapLimitRightInfo@@0AEBV1@@Z

/*
 * Function: j_??$fill@PEAVCMoveMapLimitRightInfo@@V1@@std@@YAXPEAVCMoveMapLimitRightInfo@@0AEBV1@@Z
 * Address: 0x14001310B
 */

void __fastcall std::fill<CMoveMapLimitRightInfo *,CMoveMapLimitRightInfo>(CMoveMapLimitRightInfo *_First, CMoveMapLimitRightInfo *_Last, CMoveMapLimitRightInfo *_Val)
{
  std::fill<CMoveMapLimitRightInfo *,CMoveMapLimitRightInfo>(_First, _Last, _Val);
}

