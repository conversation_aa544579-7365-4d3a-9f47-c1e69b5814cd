﻿#pragma once

// Generated from decompiled code for NexusPro
// Class: vector_CMoveMapLimitInfo_____ptr64_std
// Category: world

#include "../common/WindowsTypes.h"
#include "../common/Stubs.h"

// Forward declarations
class CMoveMapLimitInfo;

class vector_CMoveMapLimitInfo_____ptr64_std {
public:
    // Constructor/Destructor
    vector_CMoveMapLimitInfo_____ptr64_std();
    virtual ~vector_CMoveMapLimitInfo_____ptr64_std();

    // Methods
    int _std(__int64 a1, __int64 a2);
    void _std(__int64 a1, __int64 a2);
    void _std(__int64 a1, __int64 a2);
    void _std(__int64 a1, __int64 a2);
    void _std(__int64 a1, __int64 a2);
    void _std(__int64 a1, __int64 a2);
    void _std(__int64 a1, __int64 a2);
    void _std(__int64 a1, __int64 a2);
    void _std(__int64 a1, __int64 a2);
    void _std(__int64 a1, __int64 a2);

    // Member variables (add as discovered from decompiled code)
    // TODO: Add member variables based on usage analysis

private:
    // Private members as needed
};
