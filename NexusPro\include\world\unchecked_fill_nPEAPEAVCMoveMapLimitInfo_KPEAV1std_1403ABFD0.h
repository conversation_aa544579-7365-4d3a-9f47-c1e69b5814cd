﻿#pragma once

// Generated from decompiled code for NexusPro
// Original address: 0x1403ABFD0
// Function: ??$unchecked_fill_n@PEAPEAVCMoveMapLimitInfo@@_KPEAV1@@stdext@@YAXPEAPEAVCMoveMapLimitInfo@@_KAEBQEAV1@@Z
// Category: world

#include "../common/WindowsTypes.h"
#include "../common/Stubs.h"

// Forward declarations
class CWorld;
class CField;
class CMap;
class CZone;
class CGameObject;
class CPlayer;

// Function declarations
// TODO: Add proper function declarations based on decompiled content

