﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x1400126CF
// Function: j_??$_Fill@PEAPEAVCMoveMapLimitRight@@PEAV1@@std@@YAXPEAPEAVCMoveMapLimitRight@@0AEBQEAV1@@Z

/*
 * Function: j_??$_Fill@PEAPEAVCMoveMapLimitRight@@PEAV1@@std@@YAXPEAPEAVCMoveMapLimitRight@@0AEBQEAV1@@Z
 * Address: 0x1400126CF
 */

void __fastcall std::_Fill<CMoveMapLimitRight * *,CMoveMapLimitRight *>(CMoveMapLimitRight **_First, CMoveMapLimitRight **_Last, CMoveMapLimitRight *const *_Val)
{
  std::_Fill<CMoveMapLimitRight * *,CMoveMapLimitRight *>(_First, _Last, _Val);
}

