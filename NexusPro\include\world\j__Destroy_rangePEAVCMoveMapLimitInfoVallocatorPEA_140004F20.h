﻿#pragma once

// Generated from decompiled code for NexusPro
// Original address: 0x140004F20
// Function: j_??$_Destroy_range@PEAVCMoveMapLimitInfo@@V?$allocator@PEAVCMoveMapLimitInfo@@@std@@@std@@YAXPEAPEAVCMoveMapLimitInfo@@0AEAV?$allocator@PEAVCMoveMapLimitInfo@@@0@@Z
// Category: world

#include "../common/WindowsTypes.h"
#include "../common/Stubs.h"

// Forward declarations
class CWorld;
class CField;
class CMap;
class CZone;
class CGameObject;
class CPlayer;

// Function declarations
// TODO: Add proper function declarations based on decompiled content

