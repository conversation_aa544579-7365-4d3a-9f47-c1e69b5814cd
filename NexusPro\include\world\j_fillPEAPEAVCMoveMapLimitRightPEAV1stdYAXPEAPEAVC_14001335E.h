﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x14001335E
// Function: j_??$fill@PEAPEAVCMoveMapLimitRight@@PEAV1@@std@@YAXPEAPEAVCMoveMapLimitRight@@0AEBQEAV1@@Z

/*
 * Function: j_??$fill@PEAPEAVCMoveMapLimitRight@@PEAV1@@std@@YAXPEAPEAVCMoveMapLimitRight@@0AEBQEAV1@@Z
 * Address: 0x14001335E
 */

void __fastcall std::fill<CMoveMapLimitRight * *,CMoveMapLimitRight *>(CMoveMapLimitRight **_First, CMoveMapLimitRight **_Last, CMoveMapLimitRight *const *_Val)
{
  std::fill<CMoveMapLimitRight * *,CMoveMapLimitRight *>(_First, _Last, _Val);
}

