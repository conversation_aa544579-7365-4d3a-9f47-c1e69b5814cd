﻿#pragma once

// Generated from decompiled code for NexusPro
// Original address: 0x1401F61B0
// Function: ?pc_AlterWorldService@CMainThread@@QEAAX_N@Z
// Category: world

#include "../common/WindowsTypes.h"
#include "../common/Stubs.h"

// Forward declarations
class CWorld;
class CField;
class CMap;
class CZone;
class CGameObject;
class CPlayer;

// Function declarations
// TODO: Add proper function declarations based on decompiled content

