﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x140010226
// Function: j_?destroy@?$allocator@PEAVCMoveMapLimitRight@@@std@@QEAAXPEAPEAVCMoveMapLimitRight@@@Z

/*
 * Function: j_?destroy@?$allocator@PEAVCMoveMapLimitRight@@@std@@QEAAXPEAPEAVCMoveMapLimitRight@@@Z
 * Address: 0x140010226
 */

void __fastcall std::allocator<CMoveMapLimitRight *>::destroy(std::allocator<CMoveMapLimitRight *> *this, CMoveMapLimitRight **_Ptr)
{
  std::allocator<CMoveMapLimitRight *>::destroy(this, _Ptr);
}

