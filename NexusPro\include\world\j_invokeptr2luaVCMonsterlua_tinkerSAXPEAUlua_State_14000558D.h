﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x14000558D
// Function: j_?invoke@?$ptr2lua@VCMonster@@@lua_tinker@@SAXPEAUlua_State@@PEAVCMonster@@@Z

/*
 * Function: j_?invoke@?$ptr2lua@VCMonster@@@lua_tinker@@SAXPEAUlua_State@@PEAVCMonster@@@Z
 * Address: 0x14000558D
 */

void __fastcall lua_tinker::ptr2lua<CMonster>::invoke(struct lua_State *L, CMonster *input)
{
  lua_tinker::ptr2lua<CMonster>::invoke(L, input);
}

