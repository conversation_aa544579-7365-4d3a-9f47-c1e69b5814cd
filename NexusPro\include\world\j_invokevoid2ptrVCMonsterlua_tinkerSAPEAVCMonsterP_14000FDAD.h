﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x14000FDAD
// Function: j_?invoke@?$void2ptr@VCMonster@@@lua_tinker@@SAPEAVCMonster@@PEAX@Z

/*
 * Function: j_?invoke@?$void2ptr@VCMonster@@@lua_tinker@@SAPEAVCMonster@@PEAX@Z
 * Address: 0x14000FDAD
 */

CMonster *__fastcall lua_tinker::void2ptr<CMonster>::invoke(void *input)
{
  return lua_tinker::void2ptr<CMonster>::invoke(input);
}

