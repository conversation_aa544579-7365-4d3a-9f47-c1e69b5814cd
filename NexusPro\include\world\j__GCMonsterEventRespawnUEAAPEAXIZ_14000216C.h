﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x14000216C
// Function: j_??_GCMonsterEventRespawn@@UEAAPEAXI@Z

/*
 * Function: j_??_GCMonsterEventRespawn@@UEAAPEAXI@Z
 * Address: 0x14000216C
 */

void *__fastcall CMonsterEventRespawn::`scalar deleting destructor'(CMonsterEventRespawn *this, unsigned int a2)
{
  return CMonsterEventRespawn::`scalar deleting destructor'(this, a2);
}

