﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x140012A80
// Function: j_?invoke@?$lua2object@PEAVCMonster@@@lua_tinker@@SAPEAVCMonster@@PEAUlua_State@@H@Z

/*
 * Function: j_?invoke@?$lua2object@PEAVCMonster@@@lua_tinker@@SAPEAVCMonster@@PEAUlua_State@@H@Z
 * Address: 0x140012A80
 */

CMonster *__fastcall lua_tinker::lua2object<CMonster *>::invoke(lua_tinker::lua2object<CMonster *> *this, struct lua_State *L, int index)
{
  return lua_tinker::lua2object<CMonster *>::invoke(this, L, index);
}

