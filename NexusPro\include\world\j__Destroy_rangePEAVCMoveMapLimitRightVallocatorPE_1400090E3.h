﻿#pragma once

// Generated from decompiled code for NexusPro
// Original address: 0x1400090E3
// Function: j_??$_Destroy_range@PEAVCMoveMapLimitRight@@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@std@@YAXPEAPEAVCMoveMapLimitRight@@0AEAV?$allocator@PEAVCMoveMapLimitRight@@@0@U_Scalar_ptr_iterator_tag@0@@Z
// Category: world

#include "../common/WindowsTypes.h"
#include "../common/Stubs.h"

// Forward declarations
class CWorld;
class CField;
class CMap;
class CZone;
class CGameObject;
class CPlayer;

// Function declarations
// TODO: Add proper function declarations based on decompiled content

