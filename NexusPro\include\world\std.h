﻿#pragma once

// Generated from decompiled code for NexusPro
// Class: std
// Category: world

#include "../common/WindowsTypes.h"
#include "../common/Stubs.h"

// Forward declarations
class CAsyncLogInfo;
class CBHPEAVCAsyncLogInfo;
class CBHPEAVCNationSettingFactory;
class CBHPEBU_TimeItem_fld;
class CBV;
class CNationSettingFactory;

class std {
public:
    // Constructor/Destructor
    std();
    virtual ~std();

    // Methods
    void _N(std::pair<std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Iterator<0>,bool> *, std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Iterator<0> *_Val1, const bool *_Val2);
    void _N(std::pair<std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Iterator<0>,bool> *);
    int64_t G(__int64 a1);
    int D(__int64 a1);
    int64_t G(__int64 a1);
    void D(__int64 a1, __int64 a2);
    void D(__int64 a1, __int64 a2);
    void D(__int64 a1, __int64 a2);
    void D(__int64 a1, __int64 a2);
    void D(__int64 a1, __int64 a2);
    void D(__int64 a1, __int64 a2);
    void _N(std::pair<std::list<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > >::_Iterator<0>,bool> *, std::list<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > >::_Iterator<0> *_Val1, const bool *_Val2);
    void _N(std::pair<std::list<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > >::_Iterator<0>,bool> *);
    void H(stdext::hash_compare<int,std::less<int> > *);
    unsigned int64_t H();
    bool H();
    void _N(std::pair<std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Iterator<0>,bool> *, std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Iterator<0> *_Val1, const bool *_Val2);
    void _N(std::pair<std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Iterator<0>,bool> *);
    int64_t D(__int64 a1, __int64 a2);
    void E(char *_First, char *_Last, char *_Dest, std::allocator<unsigned char> *_Al);
    // ... and 158 more methods

    // Member variables (add as discovered from decompiled code)
    // TODO: Add member variables based on usage analysis

private:
    // Private members as needed
};
