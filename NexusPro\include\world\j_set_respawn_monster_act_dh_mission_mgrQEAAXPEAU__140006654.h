﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x140006654
// Function: j_?set@_respawn_monster_act@_dh_mission_mgr@@QEAAXPEAU__respawn_monster@@@Z

/*
 * Function: j_?set@_respawn_monster_act@_dh_mission_mgr@@QEAAXPEAU__respawn_monster@@@Z
 * Address: 0x140006654
 */

void __fastcall _dh_mission_mgr::_respawn_monster_act::set(_dh_mission_mgr::_respawn_monster_act *this, __respawn_monster *data)
{
  _dh_mission_mgr::_respawn_monster_act::set(this, data);
}

