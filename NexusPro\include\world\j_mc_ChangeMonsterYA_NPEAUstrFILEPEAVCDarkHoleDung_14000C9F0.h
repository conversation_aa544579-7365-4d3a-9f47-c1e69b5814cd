﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x14000C9F0
// Function: j_?mc_ChangeMonster@@YA_NPEAUstrFILE@@PEAVCDarkHoleDungeonQuestSetup@@PEAD@Z

/*
 * Function: j_?mc_ChangeMonster@@YA_NPEAUstrFILE@@PEAVCDarkHoleDungeonQuestSetup@@PEAD@Z
 * Address: 0x14000C9F0
 */

bool __fastcall mc_ChangeMonster(strFILE *fstr, CDarkHoleDungeonQuestSetup *pSetup, char *pszoutErrMsg)
{
  return mc_ChangeMonster(fstr, pSetup, pszoutErrMsg);
}

