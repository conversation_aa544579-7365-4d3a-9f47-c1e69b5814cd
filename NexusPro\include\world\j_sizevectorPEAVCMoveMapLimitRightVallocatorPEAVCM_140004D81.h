﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x140004D81
// Function: j_?size@?$vector@PEAVCMoveMapLimitRight@@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@std@@QEBA_KXZ

/*
 * Function: j_?size@?$vector@PEAVCMoveMapLimitRight@@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@std@@QEBA_KXZ
 * Address: 0x140004D81
 */

unsigned __int64 __fastcall std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::size(std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *this)
{
  return std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::size(this);
}

