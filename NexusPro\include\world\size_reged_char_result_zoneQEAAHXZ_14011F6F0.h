﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x14011F6F0
// Function: ?size@_reged_char_result_zone@@QEAAHXZ

/*
 * Function: ?size@_reged_char_result_zone@@QEAAHXZ
 * Address: 0x14011F6F0
 */

signed __int64 __fastcall _reged_char_result_zone::size(_reged_char_result_zone *this)
{
  if ( this->byCharNum > 3 )
    this->byCharNum = 0;
  return 221 - 69i64 * (3 - this->byCharNum);
}

