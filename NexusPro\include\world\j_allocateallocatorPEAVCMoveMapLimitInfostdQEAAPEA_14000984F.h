﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x14000984F
// Function: j_?allocate@?$allocator@PEAVCMoveMapLimitInfo@@@std@@QEAAPEAPEAVCMoveMapLimitInfo@@_K@Z

/*
 * Function: j_?allocate@?$allocator@PEAVCMoveMapLimitInfo@@@std@@QEAAPEAPEAVCMoveMapLimitInfo@@_K@Z
 * Address: 0x14000984F
 */

CMoveMapLimitInfo **__fastcall std::allocator<CMoveMapLimitInfo *>::allocate(std::allocator<CMoveMapLimitInfo *> *this, unsigned __int64 _Count)
{
  return std::allocator<CMoveMapLimitInfo *>::allocate(this, _Count);
}

