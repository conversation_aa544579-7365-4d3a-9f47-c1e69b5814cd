﻿#pragma once

// Generated from decompiled code for NexusPro
// Original address: 0x1403A21E0
// Function: ?size@?$vector@VCMoveMapLimitRightInfo@@V?$allocator@VCMoveMapLimitRightInfo@@@std@@@std@@QEBA_KXZ
// Category: world

#include "../common/WindowsTypes.h"
#include "../common/Stubs.h"

// Forward declarations
class CWorld;
class CField;
class CMap;
class CZone;
class CGameObject;
class CPlayer;

// Function declarations
// TODO: Add proper function declarations based on decompiled content

