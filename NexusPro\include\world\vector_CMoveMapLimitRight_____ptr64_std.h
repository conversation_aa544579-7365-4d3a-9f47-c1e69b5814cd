﻿#pragma once

// Generated from decompiled code for NexusPro
// Class: vector_CMoveMapLimitRight_____ptr64_std
// Category: world

#include "../common/WindowsTypes.h"
#include "../common/Stubs.h"

// Forward declarations
class CMoveMapLimitRight;

class vector_CMoveMapLimitRight_____ptr64_std {
public:
    // Constructor/Destructor
    vector_CMoveMapLimitRight_____ptr64_std();
    virtual ~vector_CMoveMapLimitRight_____ptr64_std();

    // Methods
    void _std(__int64 a1, __int64 a2);
    void _std(__int64 a1, __int64 a2);
    void _std(__int64 a1, __int64 a2);
    void _std(__int64 a1, __int64 a2);
    void _std(__int64 a1, __int64 a2);
    void _std(__int64 a1, __int64 a2);
    void _std(__int64 a1, __int64 a2);
    void _std(__int64 a1, __int64 a2);
    void _std(__int64 a1, __int64 a2);
    void _std(__int64 a1, __int64 a2);
    void _std(__int64 a1, __int64 a2);
    void _std(__int64 a1, __int64 a2);
    void _std(__int64 a1, __int64 a2);
    int _std(__int64 a1, __int64 a2);
    void _std(__int64 a1, __int64 a2);
    void _std(__int64 a1, __int64 a2);
    void _std(__int64 a1, __int64 a2);

    // Member variables (add as discovered from decompiled code)
    // TODO: Add member variables based on usage analysis

private:
    // Private members as needed
};
