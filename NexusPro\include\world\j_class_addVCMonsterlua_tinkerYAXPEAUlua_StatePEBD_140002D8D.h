﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x140002D8D
// Function: j_??$class_add@VCMonster@@@lua_tinker@@YAXPEAUlua_State@@PEBD@Z

/*
 * Function: j_??$class_add@VCMonster@@@lua_tinker@@YAXPEAUlua_State@@PEBD@Z
 * Address: 0x140002D8D
 */

void __fastcall lua_tinker::class_add<CMonster>(struct lua_State *L, const char *name)
{
  lua_tinker::class_add<CMonster>(L, name);
}

