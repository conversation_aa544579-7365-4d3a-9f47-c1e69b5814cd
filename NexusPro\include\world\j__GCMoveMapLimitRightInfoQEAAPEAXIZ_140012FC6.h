﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x140012FC6
// Function: j_??_GCMoveMapLimitRightInfo@@QEAAPEAXI@Z

/*
 * Function: j_??_GCMoveMapLimitRightInfo@@QEAAPEAXI@Z
 * Address: 0x140012FC6
 */

void *__fastcall CMoveMapLimitRightInfo::`scalar deleting destructor'(CMoveMapLimitRightInfo *this, unsigned int a2)
{
  return CMoveMapLimitRightInfo::`scalar deleting destructor'(this, a2);
}

