﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x140009070
// Function: j_?invoke@?$void2type@PEAVCMonster@@@lua_tinker@@SAPEAVCMonster@@PEAX@Z

/*
 * Function: j_?invoke@?$void2type@PEAVCMonster@@@lua_tinker@@SAPEAVCMonster@@PEAX@Z
 * Address: 0x140009070
 */

CMonster *__fastcall lua_tinker::void2type<CMonster *>::invoke(lua_tinker::void2type<CMonster *> *this, void *ptr)
{
  return lua_tinker::void2type<CMonster *>::invoke(this, ptr);
}

