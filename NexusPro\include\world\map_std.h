﻿#pragma once

// Generated from decompiled code for NexusPro
// Class: map_std
// Category: world

#include "../common/WindowsTypes.h"
#include "../common/Stubs.h"

// Forward declarations

class map_std {
public:
    // Constructor/Destructor
    map_std();
    virtual ~map_std();

    // Methods
    void _std(__int64 a1, __int64 a2);
    void _std(__int64 a1, __int64 a2);
    void _std(__int64 a1, __int64 a2);
    void _std(__int64 a1, __int64 a2);
    void _std(__int64 a1, __int64 a2);

    // Member variables (add as discovered from decompiled code)
    // TODO: Add member variables based on usage analysis

private:
    // Private members as needed
};
