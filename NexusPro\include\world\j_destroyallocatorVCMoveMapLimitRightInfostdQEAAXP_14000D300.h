﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x14000D300
// Function: j_?destroy@?$allocator@VCMoveMapLimitRightInfo@@@std@@QEAAXPEAVCMoveMapLimitRightInfo@@@Z

/*
 * Function: j_?destroy@?$allocator@VCMoveMapLimitRightInfo@@@std@@QEAAXPEAVCMoveMapLimitRightInfo@@@Z
 * Address: 0x14000D300
 */

void __fastcall std::allocator<CMoveMapLimitRightInfo>::destroy(std::allocator<CMoveMapLimitRightInfo> *this, CMoveMapLimitRightInfo *_Ptr)
{
  std::allocator<CMoveMapLimitRightInfo>::destroy(this, _Ptr);
}

