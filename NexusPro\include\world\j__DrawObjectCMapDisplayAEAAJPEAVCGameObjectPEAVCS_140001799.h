﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x140001799
// Function: j_?_DrawObject@CMapDisplay@@AEAAJPEAVCGameObject@@PEAVCSurface@@@Z

/*
 * Function: j_?_DrawObject@CMapDisplay@@AEAAJPEAVCGameObject@@PEAVCSurface@@@Z
 * Address: 0x140001799
 */

HRESULT __fastcall CMapDisplay::_DrawObject(CMapDisplay *this, CGameObject *pObj, CSurface *pSF)
{
  return CMapDisplay::_DrawObject(this, pObj, pSF);
}

