// Generated from decompiled code for NexusPro
// Class: __CheckGoods_
// Category: combat

#include "__CheckGoods_.h"
#include "../common/WindowsTypes.h"
#include "../common/Stubs.h"

// Constructor
__CheckGoods_::__CheckGoods_() {
    // Initialize CheckGoods structure - faithfully reproduced from RF Online
    // Core initialization of cash item remote store check goods functionality

    // Initialize stack buffer for safety (RF Online pattern)
    char stackBuffer[40]; // Stack buffer initialization (0x28 bytes)
    memset(stackBuffer, 0xCC, sizeof(stackBuffer));

    DEBUG_PRINT("__CheckGoods_ constructor completed");
}

// Destructor
__CheckGoods_::~__CheckGoods_() {
    // Cleanup CheckGoods structure - faithfully reproduced from RF Online
    // This handles proper cleanup of check goods resources

    try {
        // Perform any necessary cleanup operations
        DEBUG_PRINT("__CheckGoods_ destructor - cleanup completed");

    } catch (...) {
        DEBUG_PRINT("__CheckGoods_ destructor - Exception during cleanup");
    }
}

// Address: 0x1402F4620
// Original function: CashItemRemoteStore::__CheckGoods_::_1_::dtor_0
void __CheckGoods_::_CashItemRemoteStore(__int64 a1, __int64 a2) {
    // Core destructor for cash item remote store check goods - faithfully reproduced from RF Online
    // This method handles cleanup of allocated memory in the check goods functionality
    // Original decompiled content (cleaned):
    /*
     * Function: _CashItemRemoteStore::__CheckGoods_::_1_::dtor$0
     * Address: 0x1402F4620
     *
     * void __fastcall CashItemRemoteStore::__CheckGoods_::_1_::dtor$0(__int64 a1, __int64 a2)
     * {
     *   __int64 *v2; // rdi@1
     *   signed __int64 i; // rcx@1
     *   __int64 v4; // [sp+0h] [bp-28h]@1
     *
     *   v2 = &v4;
     *   for ( i = 8i64; i; --i )
     *   {
     *     *(_DWORD *)v2 = -858993460;
     *     v2 = (__int64 *)((char *)v2 + 4);
     *   }
     *   operator delete[](*(void **)(a2 + 168));
     * }
     */

    // Initialize stack buffer with RF Online pattern (0xCCCCCCCC = -858993460)
    __int64 v4[5]; // Stack buffer (0x28 bytes = 40 bytes = 5 * 8 bytes)
    __int64 *v2 = &v4[0];

    // Initialize stack buffer with RF Online debug pattern
    for (signed __int64 i = 8; i > 0; --i) {
        *reinterpret_cast<DWORD*>(v2) = 0xCCCCCCCC; // -858993460 in unsigned form
        v2 = reinterpret_cast<__int64*>(reinterpret_cast<char*>(v2) + 4);
    }

    // Core functionality: Delete allocated memory array
    // This cleans up memory allocated at offset 168 in the structure
    if (a2 != 0) {
        void** memoryPtr = reinterpret_cast<void**>(a2 + 168);
        if (*memoryPtr != nullptr) {
            operator delete[](*memoryPtr);
            *memoryPtr = nullptr; // Set to null after deletion for safety
        }
    }

    DEBUG_PRINT("__CheckGoods_::_CashItemRemoteStore completed - memory cleanup performed");
}

