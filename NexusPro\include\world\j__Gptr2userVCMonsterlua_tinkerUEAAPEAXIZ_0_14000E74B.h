﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x14000E74B
// Function: j_??_G?$ptr2user@VCMonster@@@lua_tinker@@UEAAPEAXI@Z_0

/*
 * Function: j_??_G?$ptr2user@VCMonster@@@lua_tinker@@UEAAPEAXI@Z_0
 * Address: 0x14000E74B
 */

void *__fastcall lua_tinker::ptr2user<CMonster>::`scalar deleting destructor'(lua_tinker::ptr2user<CMonster> *this, unsigned int a2)
{
  return lua_tinker::ptr2user<CMonster>::`scalar deleting destructor'(this, a2);
}

