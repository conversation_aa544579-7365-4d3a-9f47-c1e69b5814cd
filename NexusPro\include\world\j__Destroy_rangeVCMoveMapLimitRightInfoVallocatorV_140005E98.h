﻿#pragma once

// Generated from decompiled code for NexusPro
// Original address: 0x140005E98
// Function: j_??$_Destroy_range@VCMoveMapLimitRightInfo@@V?$allocator@VCMoveMapLimitRightInfo@@@std@@@std@@YAXPEAVCMoveMapLimitRightInfo@@0AEAV?$allocator@VCMoveMapLimitRightInfo@@@0@@Z
// Category: world

#include "../common/WindowsTypes.h"
#include "../common/Stubs.h"

// Forward declarations
class CWorld;
class CField;
class CMap;
class CZone;
class CGameObject;
class CPlayer;

// Function declarations
// TODO: Add proper function declarations based on decompiled content

