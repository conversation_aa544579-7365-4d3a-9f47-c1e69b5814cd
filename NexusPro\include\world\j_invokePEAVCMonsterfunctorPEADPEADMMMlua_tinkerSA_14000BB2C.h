﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x14000BB2C
// Function: j_??$invoke@PEAVCMonster@@@?$functor@PEADPEADMMM@lua_tinker@@SAHPEAUlua_State@@@Z

/*
 * Function: j_??$invoke@PEAVCMonster@@@?$functor@PEADPEADMMM@lua_tinker@@SAHPEAUlua_State@@@Z
 * Address: 0x14000BB2C
 */

int lua_tinker::functor<char *,char *,float,float,float>::invoke<CMonster *>()
{
  return lua_tinker::functor<char *,char *,float,float,float>::invoke<CMonster *>();
}

