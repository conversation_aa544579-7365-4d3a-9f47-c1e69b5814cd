﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x140013F25
// Function: j_?_Tidy@?$vector@VCMoveMapLimitRightInfo@@V?$allocator@VCMoveMapLimitRightInfo@@@std@@@std@@IEAAXXZ

/*
 * Function: j_?_Tidy@?$vector@VCMoveMapLimitRightInfo@@V?$allocator@VCMoveMapLimitRightInfo@@@std@@@std@@IEAAXXZ
 * Address: 0x140013F25
 */

void __fastcall std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::_Tidy(std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *this)
{
  std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::_Tidy(this);
}

