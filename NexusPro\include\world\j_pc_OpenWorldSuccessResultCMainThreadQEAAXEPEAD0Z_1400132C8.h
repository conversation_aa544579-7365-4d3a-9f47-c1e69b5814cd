﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x1400132C8
// Function: j_?pc_OpenWorldSuccessResult@CMainThread@@QEAAXEPEAD0@Z

/*
 * Function: j_?pc_OpenWorldSuccessResult@CMainThread@@QEAAXEPEAD0@Z
 * Address: 0x1400132C8
 */

void __fastcall CMainThread::pc_OpenWorldSuccessResult(CMainThread *this, char byWorldCode, char *pszDBName, char *pszDBIP)
{
  CMainThread::pc_OpenWorldSuccessResult(this, byWorldCode, pszDBName, pszDBIP);
}

