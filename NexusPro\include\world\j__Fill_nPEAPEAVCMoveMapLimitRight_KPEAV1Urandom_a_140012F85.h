﻿#pragma once

// Generated from decompiled code for NexusPro
// Original address: 0x140012F85
// Function: j_??$_Fill_n@PEAPEAVCMoveMapLimitRight@@_KPEAV1@Urandom_access_iterator_tag@std@@@std@@YAXPEAPEAVCMoveMapLimitRight@@_KAEBQEAV1@Urandom_access_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
// Category: world

#include "../common/WindowsTypes.h"
#include "../common/Stubs.h"

// Forward declarations
class CWorld;
class CField;
class CMap;
class CZone;
class CGameObject;
class CPlayer;

// Function declarations
// TODO: Add proper function declarations based on decompiled content

