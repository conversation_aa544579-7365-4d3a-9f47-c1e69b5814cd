﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x140007C61
// Function: j_??$unchecked_fill_n@PEAPEAVCMoveMapLimitInfo@@_KPEAV1@@stdext@@YAXPEAPEAVCMoveMapLimitInfo@@_KAEBQEAV1@@Z

/*
 * Function: j_??$unchecked_fill_n@PEAPEAVCMoveMapLimitInfo@@_KPEAV1@@stdext@@YAXPEAPEAVCMoveMapLimitInfo@@_KAEBQEAV1@@Z
 * Address: 0x140007C61
 */

void __fastcall stdext::unchecked_fill_n<CMoveMapLimitInfo * *,unsigned __int64,CMoveMapLimitInfo *>(CMoveMapLimitInfo **_First, unsigned __int64 _Count, CMoveMapLimitInfo *const *_Val)
{
  stdext::unchecked_fill_n<CMoveMapLimitInfo * *,unsigned __int64,CMoveMapLimitInfo *>(_First, _Count, _Val);
}

