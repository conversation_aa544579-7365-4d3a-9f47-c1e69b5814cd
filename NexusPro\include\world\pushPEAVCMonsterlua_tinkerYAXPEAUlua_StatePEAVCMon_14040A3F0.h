﻿#pragma once

// Generated from decompiled code for NexusPro
// Original address: 0x14040A3F0
// Function: ??$push@PEAVCMonster@@@lua_tinker@@YAXPEAUlua_State@@PEAVCMonster@@@Z
// Category: world

#include "../common/WindowsTypes.h"
#include "../common/Stubs.h"

// Forward declarations
class CWorld;
class CField;
class CMap;
class CZone;
class CGameObject;
class CPlayer;

// Function declarations
// TODO: Add proper function declarations based on decompiled content

