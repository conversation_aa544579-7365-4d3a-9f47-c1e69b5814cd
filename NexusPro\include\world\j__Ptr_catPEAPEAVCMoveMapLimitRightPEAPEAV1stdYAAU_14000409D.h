﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x14000409D
// Function: j_??$_Ptr_cat@PEAPEAVCMoveMapLimitRight@@PEAPEAV1@@std@@YA?AU_Scalar_ptr_iterator_tag@0@AEAPEAPEAVCMoveMapLimitRight@@0@Z

/*
 * Function: j_??$_Ptr_cat@PEAPEAVCMoveMapLimitRight@@PEAPEAV1@@std@@YA?AU_Scalar_ptr_iterator_tag@0@AEAPEAPEAVCMoveMapLimitRight@@0@Z
 * Address: 0x14000409D
 */

std::_Scalar_ptr_iterator_tag __fastcall std::_Ptr_cat<CMoveMapLimitRight * *,CMoveMapLimitRight * *>(CMoveMapLimitRight ***__formal, CMoveMapLimitRight ***a2)
{
  return std::_Ptr_cat<CMoveMapLimitRight * *,CMoveMapLimitRight * *>(__formal, a2);
}

