﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x14000A146
// Function: j_??$_Iter_cat@PEAPEAVCMoveMapLimitRight@@@std@@YA?AUrandom_access_iterator_tag@0@AEBQEAPEAVCMoveMapLimitRight@@@Z

/*
 * Function: j_??$_Iter_cat@PEAPEAVCMoveMapLimitRight@@@std@@YA?AUrandom_access_iterator_tag@0@AEBQEAPEAVCMoveMapLimitRight@@@Z
 * Address: 0x14000A146
 */

std::random_access_iterator_tag __fastcall std::_Iter_cat<CMoveMapLimitRight * *>(CMoveMapLimitRight **const *__formal)
{
  return std::_Iter_cat<CMoveMapLimitRight * *>(__formal);
}

