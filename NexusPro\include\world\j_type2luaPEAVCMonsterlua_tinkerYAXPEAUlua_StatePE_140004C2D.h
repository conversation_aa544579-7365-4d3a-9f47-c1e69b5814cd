﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x140004C2D
// Function: j_??$type2lua@PEAVCMonster@@@lua_tinker@@YAXPEAUlua_State@@PEAVCMonster@@@Z

/*
 * Function: j_??$type2lua@PEAVCMonster@@@lua_tinker@@YAXPEAUlua_State@@PEAVCMonster@@@Z
 * Address: 0x140004C2D
 */

void __fastcall lua_tinker::type2lua<CMonster *>(struct lua_State *L, CMonster *val)
{
  lua_tinker::type2lua<CMonster *>(L, val);
}

