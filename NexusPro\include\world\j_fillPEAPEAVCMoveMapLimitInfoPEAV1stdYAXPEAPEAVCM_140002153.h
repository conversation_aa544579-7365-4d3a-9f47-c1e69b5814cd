﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x140002153
// Function: j_??$fill@PEAPEAVCMoveMapLimitInfo@@PEAV1@@std@@YAXPEAPEAVCMoveMapLimitInfo@@0AEBQEAV1@@Z

/*
 * Function: j_??$fill@PEAPEAVCMoveMapLimitInfo@@PEAV1@@std@@YAXPEAPEAVCMoveMapLimitInfo@@0AEBQEAV1@@Z
 * Address: 0x140002153
 */

void __fastcall std::fill<CMoveMapLimitInfo * *,CMoveMapLimitInfo *>(CMoveMapLimitInfo **_First, CMoveMapLimitInfo **_Last, CMoveMapLimitInfo *const *_Val)
{
  std::fill<CMoveMapLimitInfo * *,CMoveMapLimitInfo *>(_First, _Last, _Val);
}

