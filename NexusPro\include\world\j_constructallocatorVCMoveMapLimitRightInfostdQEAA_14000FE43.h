﻿#pragma once
#include "../../common/RFProtocol.h"

// Original address: 0x14000FE43
// Function: j_?construct@?$allocator@VCMoveMapLimitRightInfo@@@std@@QEAAXPEAVCMoveMapLimitRightInfo@@AEBV3@@Z

/*
 * Function: j_?construct@?$allocator@VCMoveMapLimitRightInfo@@@std@@QEAAXPEAVCMoveMapLimitRightInfo@@AEBV3@@Z
 * Address: 0x14000FE43
 */

void __fastcall std::allocator<CMoveMapLimitRightInfo>::construct(std::allocator<CMoveMapLimitRightInfo> *this, CMoveMapLimitRightInfo *_Ptr, CMoveMapLimitRightInfo *_Val)
{
  std::allocator<CMoveMapLimitRightInfo>::construct(this, _Ptr, _Val);
}

